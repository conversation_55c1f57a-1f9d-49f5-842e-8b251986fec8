{"version": 3, "file": "combination.d.ts", "sourceRoot": "", "sources": ["../src/combination.ts"], "names": [], "mappings": "AAAA,OAAO,EAAS,UAAU,EAAQ,QAAQ,EAAY,oBAAoB,EAAgE,MAAM,UAAU,CAAC;AAgC3J,wBAAgB,KAAK,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC,CAAC,CActC;AACD,wBAAgB,KAAK,CAClB,CAAC,SAAS,SAAS,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAW3F;AAED,wBAAgB,IAAI,CACjB,CAAC,SAAS,SAAS,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAyB3F;AAGD,wBAAgB,MAAM,CACnB,CAAC,SAAS,SAAS,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAc3F;AAED,wBAAgB,WAAW,CAAC,CAAC,EAAE,UAAU,EAAE,MAAM,GAAG,QAAQ,CAAC,CAAC,CAAC,CAiB9D;AACD,wBAAgB,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,MAAM,OAAO,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAElH;AACD,wBAAgB,aAAa,CAC1B,CAAC,SAAS,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CA2B1E;AAGD,wBAAgB,GAAG,CAChB,CAAC,SAAS,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,oBAAoB,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAuB1E;AAED,wBAAgB,SAAS,CAAC,CAAC,EAAE,CAAC,SAAS,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,MAAM,CAAC,CAAC,CAQtF;AAiBD,eAAO,MAAM,cAAc,sFAA4C,CAAC;AAyCxE,eAAO,MAAM,WAAW,oFAAsC,CAAC;AA4B/D,eAAO,MAAM,MAAM,+DAA4B,CAAC"}