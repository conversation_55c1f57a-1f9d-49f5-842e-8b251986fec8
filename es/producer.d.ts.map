{"version": 3, "file": "producer.d.ts", "sourceRoot": "", "sources": ["../src/producer.ts"], "names": [], "mappings": "AACA,OAAO,EAAS,UAAU,EAAW,QAAQ,EAAU,YAAY,EAAE,eAAe,EAAE,MAAM,UAAU,CAAC;AACvG,MAAM,MAAM,OAAO,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;AACrD,wBAAgB,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,cAYhD;AACD,wBAAgB,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,UAAU,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAE9D;AAYD,wBAAgB,EAAE,CAAC,CAAC,EAAE,GAAG,IAAI,EAAE,CAAC,EAAE,iBAEjC;AAED,wBAAgB,SAAS,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAE9D;AAED,wBAAgB,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAO3D;AAED,wBAAgB,KAAK,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAgBxE;AAQD,wBAAgB,gBAAgB,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC,KAAK,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAE1H;AAED,wBAAgB,SAAS,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,eAAe,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,iBAerE;AAED,wBAAgB,WAAW,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAIjE;AACD,wBAAgB,SAAS,CAAC,KAAK,EAAE,WAAW,EAAE,IAAI,CAAC,EAAE,WAAW,wBAE/D;AACD,wBAAgB,YAAY,CAAC,CAAC,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAYlE;AACD,wBAAgB,UAAU,CAAC,CAAC,EAAE,MAAM,EAAE,2BAA2B,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAenF;AACD,wBAAgB,kBAAkB,CAAC,CAAC,EAAE,MAAM,EAAE,cAAc,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,CAkB9E;AACD,wBAAgB,kBAAkB,IAAI,UAAU,CAAC,mBAAmB,CAAC,CAUpE;AACD,wBAAgB,KAAK,CAClB,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC,CAMnD;AAED,wBAAgB,YAAY,CACzB,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAOjE;AACD,wBAAgB,gBAAgB,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,CAO/F;AACD,wBAAgB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAEzC;AACD,wBAAgB,UAAU,CAAC,CAAC,EAAE,GAAG,GAAG,UAAU,CAAC,KAAK,CAAC,CAEpD;AACD,wBAAgB,KAAK,IAAI,UAAU,CAAC,KAAK,CAAC,CAEzC"}