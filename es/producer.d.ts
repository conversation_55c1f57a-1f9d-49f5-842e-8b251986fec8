import { Observable, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>D<PERSON>pachter } from "./common";
export type Subject<T> = Observable<T> & Observer<T>;
export declare function subject<T>(source?: Observable<T>): Subject<T>;
export declare function defer<T>(f: () => Observable<T>): Observable<T>;
export declare function of<T>(...data: T[]): Observable<T>;
export declare function fromArray<T>(data: ArrayLike<T>): Observable<T>;
export declare function interval(period: number): Observable<number>;
export declare function timer(delay: number, period?: number): Observable<number>;
export declare function fromEventPattern<T>(add: (n: EventHandler<T>) => void, remove: (n: EventHandler<T>) => void): Observable<T>;
export declare function fromEvent<T, N>(target: EventDispachter<N, T>, name: N): Observable<T>;
export declare function fromPromise<T>(promise: Promise<T>): Observable<T>;
export declare function fromFetch(input: RequestInfo, init?: RequestInit): Observable<Response>;
export declare function fromIterable<T>(source: Iterable<T>): Observable<T>;
export declare function fromReader<T>(source: ReadableStreamDefaultReader<T>): Observable<T>;
export declare function fromReadableStream<T>(source: ReadableStream<T>): Observable<T>;
export declare function fromAnimationFrame(): Observable<DOMHighResTimeStamp>;
export declare function range(start: number, count: number): Observable<number>;
export declare function bindCallback<T>(call: Function, thisArg: any, ...args: any[]): Observable<T>;
export declare function bindNodeCallback<T>(call: Function, thisArg: any, ...args: any[]): Observable<T>;
export declare function never(): Observable<never>;
export declare function throwError(e: any): Observable<never>;
export declare function empty(): Observable<never>;
//# sourceMappingURL=producer.d.ts.map