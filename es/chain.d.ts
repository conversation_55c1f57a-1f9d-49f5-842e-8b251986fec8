import { Observable, Operator } from './common';
import * as producer from './producer';
import { subscribe, toPromise } from './utils';
import * as combination from './combination';
declare const observables: {
    subject<T>(source?: Observable<T> | undefined): producer.Subject<T>;
    defer<T_1>(f: () => Observable<T_1>): Observable<T_1>;
    of<T_2>(...data: T_2[]): Observable<T_2>;
    fromArray<T_3>(data: ArrayLike<T_3>): Observable<T_3>;
    interval(period: number): Observable<number>;
    timer(delay: number, period?: number | undefined): Observable<number>;
    fromEventPattern<T_4>(add: (n: import("./common").EventHandler<T_4>) => void, remove: (n: import("./common").EventHandler<T_4>) => void): Observable<T_4>;
    fromEvent<T_5, N>(target: import("./common").EventDispachter<N, T_5>, name: N): Observable<T_5>;
    fromPromise<T_6>(promise: Promise<T_6>): Observable<T_6>;
    fromFetch(input: RequestInfo, init?: RequestInit | undefined): Observable<Response>;
    fromIterable<T_7>(source: Iterable<T_7>): Observable<T_7>;
    fromReader<T_8>(source: ReadableStreamDefaultReader<T_8>): Observable<T_8>;
    fromReadableStream<T_9>(source: ReadableStream<T_9>): Observable<T_9>;
    fromAnimationFrame(): Observable<number>;
    range(start: number, count: number): Observable<number>;
    bindCallback<T_10>(call: Function, thisArg: any, ...args: any[]): Observable<T_10>;
    bindNodeCallback<T_11>(call: Function, thisArg: any, ...args: any[]): Observable<T_11>;
    never(): Observable<never>;
    throwError(e: any): Observable<never>;
    empty(): Observable<never>;
    zip: typeof combination.zip;
    merge: typeof combination.merge;
    race: typeof combination.race;
    concat: typeof combination.concat;
    combineLatest: typeof combination.combineLatest;
};
declare const operators: {
    scan: <T, R, ACC extends T | R>(f: (acc: ACC, c: T) => ACC, seed?: ACC | undefined) => Operator<T, ACC>;
    pairwise: <T_1>() => Operator<T_1, [T_1, T_1]>;
    map: <T_2, R_1>(mapper: (data: T_2) => R_1, thisArg?: any) => Operator<T_2, R_1>;
    mapTo: <R_2>(target: R_2) => Operator<unknown, R_2>;
    switchMap: <T_3, U, R_3 = U>(makeSource: (data: T_3, index: number) => Observable<U>, combineResults?: ((outter: T_3, inner: U) => R_3) | undefined) => Operator<T_3, R_3>;
    switchMapTo: (innerSource: Observable<unknown>, combineResults?: ((outter: unknown, inner: unknown) => unknown) | undefined) => Operator<unknown, unknown>;
    concatMap: <T_4, U_1, R_4 = U_1>(makeSource: (data: T_4, index: number) => Observable<U_1>, combineResults?: ((outter: T_4, inner: U_1) => R_4) | undefined) => Operator<T_4, R_4>;
    concatMapTo: (innerSource: Observable<unknown>, combineResults?: ((outter: unknown, inner: unknown) => unknown) | undefined) => Operator<unknown, unknown>;
    mergeMap: <T_5, U_2, R_5 = U_2>(makeSource: (data: T_5, index: number) => Observable<U_2>, combineResults?: ((outter: T_5, inner: U_2) => R_5) | undefined) => Operator<T_5, R_5>;
    mergeMapTo: (innerSource: Observable<unknown>, combineResults?: ((outter: unknown, inner: unknown) => unknown) | undefined) => Operator<unknown, unknown>;
    exhaustMap: <T_6, U_3, R_6 = U_3>(makeSource: (data: T_6, index: number) => Observable<U_3>, combineResults?: ((outter: T_6, inner: U_3) => R_6) | undefined) => Operator<T_6, R_6>;
    exhaustMapTo: (innerSource: Observable<unknown>, combineResults?: ((outter: unknown, inner: unknown) => unknown) | undefined) => Operator<unknown, unknown>;
    groupBy: <T_7>(f: (data: T_7) => any) => Operator<T_7, Observable<unknown> & import("./common").Observer<unknown> & {
        key: any;
    }>;
    timeInterval: <T_8>() => Operator<T_8, {
        value: T_8;
        interval: number;
    }>;
    bufferTime: <T_9>(miniseconds: number) => Operator<T_9, T_9[]>;
    delay: <T_10>(delay: number) => Operator<T_10, T_10>;
    catchError: <T_11, R_7 = T_11>(selector: (err: any) => Observable<R_7>) => Operator<T_11, R_7>;
    expand: <T_12>(project: (value: T_12, index: number) => Observable<T_12>) => Operator<T_12, T_12>;
    reduce: <T_13, R_8, ACC_1 extends T_13 | R_8>(f: (acc: ACC_1, c: T_13) => ACC_1, seed?: ACC_1 | undefined) => Operator<T_13, ACC_1>;
    count: <T_14>(f: (d: T_14) => boolean) => Operator<T_14, number>;
    max: () => Operator<number, number>;
    min: () => Operator<number, number>;
    sum: () => Operator<number, number>;
    filter: <T_15>(filter: (data: T_15) => boolean, thisArg?: any) => Operator<T_15, T_15>;
    ignoreElements: <T_16>() => Operator<T_16, never>;
    take: <T_17>(count: number) => Operator<T_17, T_17>;
    takeUntil: <T_18>(control: Observable<unknown>) => Operator<T_18, T_18>;
    takeWhile: <T_19>(f: (data: T_19) => boolean) => Operator<T_19, T_19>;
    takeLast: <T_20>(count: number) => Operator<T_20, T_20[]>;
    skip: <T_21>(count: number) => Operator<T_21, T_21>;
    skipUntil: <T_22>(control: Observable<unknown>) => Operator<T_22, T_22>;
    skipWhile: <T_23>(f: (data: T_23) => boolean) => Operator<T_23, T_23>;
    throttle: <T_24>(durationSelector: (data: T_24) => Observable<unknown>, config?: {
        leading: boolean;
        trailing: boolean;
    } | undefined) => Operator<T_24, T_24>;
    audit: <T_25>(durationSelector: (d: T_25) => Observable<unknown>) => Operator<T_25, T_25>;
    debounce: <T_26>(durationSelector: (d: T_26) => Observable<unknown>) => Operator<T_26, T_26>;
    debounceTime: <T_27>(period: number) => Operator<T_27, T_27>;
    elementAt: <T_28>(count: number, defaultValue?: T_28 | undefined) => Operator<T_28, T_28>;
    find: <T_29>(f: (d: T_29) => boolean) => (source: Observable<T_29>) => Observable<T_29>;
    findIndex: <T_30>(f: (d: T_30) => boolean) => Operator<T_30, number>;
    first: <T_31>(f?: ((d: T_31, index: number) => boolean) | undefined, defaultValue?: T_31 | undefined) => Operator<T_31, T_31>;
    last: <T_32>(f?: ((d: T_32, index: number) => boolean) | undefined, defaultValue?: T_32 | undefined) => Operator<T_32, T_32>;
    every: <T_33>(predicate: (d: T_33, index: number) => boolean) => Operator<T_33, boolean>;
    share<T_34>(): Operator<T_34, T_34>;
    shareReplay<T_35>(bufferSize: number): Operator<T_35, T_35>;
    iif<T_36, F>(condition: () => boolean, trueS: Observable<T_36>, falseS: Observable<F>): Observable<T_36 | F>;
    startWith<T_37, A extends unknown[]>(...xs: A): Operator<T_37, T_37 | A[number]>;
    withLatestFrom: <T_38, A_1 extends unknown[]>(...args: import("./common").ObservableInputTuple<A_1>) => Operator<T_38, [T_38, ...A_1]>;
    bufferCount: <T_39>(bufferSize: number, startBufferEvery?: number | undefined) => Operator<T_39, T_39[]>;
    buffer: <T_40>(closingNotifier: Observable<unknown>) => Operator<T_40, T_40[]>;
    tap: <T_41>(ob: Partial<import("./common").Observer<T_41>> | ((d: T_41) => void)) => Operator<T_41, T_41>;
    timeout: <T_42>(timeout: number) => Operator<T_42, T_42>;
};
type Obs = {
    subscribe: typeof subscribe;
    toPromise: typeof toPromise;
};
type Op = {
    [key in keyof (typeof operators)]: (...args: Parameters<((typeof operators))[key]>) => Op;
} & Obs;
type Rx = {
    [key in keyof typeof observables]: (...args: Parameters<(typeof observables)[key]>) => Op;
};
export declare const rx: Rx;
export {};
//# sourceMappingURL=chain.d.ts.map