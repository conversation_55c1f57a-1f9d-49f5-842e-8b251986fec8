<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1750238606771" clover="3.2.0">
  <project timestamp="1750238606771" name="All files">
    <metrics statements="807" coveredstatements="193" conditionals="166" coveredconditionals="7" methods="346" coveredmethods="49" elements="1319" coveredelements="249" complexity="0" loc="807" ncloc="807" packages="1" files="8" classes="8"/>
    <file name="combination.ts" path="/Users/<USER>/project/fastrx/src/combination.ts">
      <metrics statements="168" coveredstatements="13" conditionals="35" coveredconditionals="0" methods="63" coveredmethods="0"/>
      <line num="1" count="6" type="stmt"/>
      <line num="4" count="0" type="stmt"/>
      <line num="5" count="0" type="stmt"/>
      <line num="6" count="0" type="stmt"/>
      <line num="9" count="0" type="stmt"/>
      <line num="10" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="11" count="0" type="stmt"/>
      <line num="12" count="0" type="stmt"/>
      <line num="16" count="0" type="stmt"/>
      <line num="17" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="18" count="0" type="stmt"/>
      <line num="22" count="0" type="stmt"/>
      <line num="25" count="0" type="stmt"/>
      <line num="26" count="0" type="stmt"/>
      <line num="29" count="0" type="stmt"/>
      <line num="30" count="0" type="stmt"/>
      <line num="33" count="6" type="stmt"/>
      <line num="34" count="0" type="stmt"/>
      <line num="35" count="0" type="stmt"/>
      <line num="36" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="37" count="0" type="stmt"/>
      <line num="38" count="0" type="stmt"/>
      <line num="40" count="0" type="stmt"/>
      <line num="41" count="0" type="stmt"/>
      <line num="42" count="0" type="stmt"/>
      <line num="43" count="0" type="stmt"/>
      <line num="45" count="0" type="stmt"/>
      <line num="48" count="6" type="stmt"/>
      <line num="50" count="0" type="stmt"/>
      <line num="51" count="0" type="stmt"/>
      <line num="52" count="0" type="stmt"/>
      <line num="53" count="0" type="stmt"/>
      <line num="54" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="55" count="0" type="stmt"/>
      <line num="58" count="0" type="stmt"/>
      <line num="62" count="6" type="stmt"/>
      <line num="64" count="0" type="stmt"/>
      <line num="66" count="0" type="stmt"/>
      <line num="67" count="0" type="stmt"/>
      <line num="68" count="0" type="stmt"/>
      <line num="69" count="0" type="stmt"/>
      <line num="70" count="0" type="stmt"/>
      <line num="71" count="0" type="stmt"/>
      <line num="72" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="73" count="0" type="stmt"/>
      <line num="75" count="0" type="stmt"/>
      <line num="78" count="0" type="stmt"/>
      <line num="79" count="0" type="stmt"/>
      <line num="80" count="0" type="stmt"/>
      <line num="81" count="0" type="stmt"/>
      <line num="82" count="0" type="stmt"/>
      <line num="83" count="0" type="stmt"/>
      <line num="86" count="0" type="stmt"/>
      <line num="91" count="6" type="stmt"/>
      <line num="93" count="0" type="stmt"/>
      <line num="94" count="0" type="stmt"/>
      <line num="95" count="0" type="stmt"/>
      <line num="96" count="0" type="stmt"/>
      <line num="97" count="0" type="stmt"/>
      <line num="98" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="99" count="0" type="stmt"/>
      <line num="100" count="0" type="stmt"/>
      <line num="102" count="0" type="stmt"/>
      <line num="104" count="0" type="stmt"/>
      <line num="108" count="6" type="stmt"/>
      <line num="109" count="0" type="stmt"/>
      <line num="110" count="0" type="stmt"/>
      <line num="111" count="0" type="stmt"/>
      <line num="112" count="0" type="stmt"/>
      <line num="113" count="0" type="stmt"/>
      <line num="114" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="115" count="0" type="stmt"/>
      <line num="117" count="0" type="stmt"/>
      <line num="119" count="0" type="stmt"/>
      <line num="120" count="0" type="stmt"/>
      <line num="121" count="0" type="stmt"/>
      <line num="122" count="0" type="stmt"/>
      <line num="126" count="6" type="stmt"/>
      <line num="127" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="129" count="6" type="stmt"/>
      <line num="131" count="0" type="stmt"/>
      <line num="132" count="0" type="stmt"/>
      <line num="133" count="0" type="stmt"/>
      <line num="134" count="0" type="stmt"/>
      <line num="135" count="0" type="stmt"/>
      <line num="136" count="0" type="stmt"/>
      <line num="137" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="139" count="0" type="stmt"/>
      <line num="140" count="0" type="stmt"/>
      <line num="141" count="0" type="stmt"/>
      <line num="142" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="143" count="0" type="stmt"/>
      <line num="144" count="0" type="stmt"/>
      <line num="145" count="0" type="stmt"/>
      <line num="147" count="0" type="stmt"/>
      <line num="149" count="0" type="stmt"/>
      <line num="152" count="0" type="stmt"/>
      <line num="153" count="0" type="stmt"/>
      <line num="155" count="0" type="stmt"/>
      <line num="160" count="6" type="stmt"/>
      <line num="162" count="0" type="stmt"/>
      <line num="163" count="0" type="stmt"/>
      <line num="164" count="0" type="stmt"/>
      <line num="165" count="0" type="stmt"/>
      <line num="166" count="0" type="stmt"/>
      <line num="167" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="169" count="0" type="stmt"/>
      <line num="170" count="0" type="stmt"/>
      <line num="171" count="0" type="stmt"/>
      <line num="172" count="0" type="stmt"/>
      <line num="173" count="0" type="stmt"/>
      <line num="174" count="0" type="stmt"/>
      <line num="175" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="176" count="0" type="stmt"/>
      <line num="179" count="0" type="stmt"/>
      <line num="180" count="0" type="stmt"/>
      <line num="182" count="0" type="stmt"/>
      <line num="186" count="6" type="stmt"/>
      <line num="187" count="0" type="stmt"/>
      <line num="188" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="189" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="190" count="0" type="stmt"/>
      <line num="192" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="199" count="0" type="stmt"/>
      <line num="200" count="0" type="stmt"/>
      <line num="201" count="0" type="stmt"/>
      <line num="202" count="0" type="stmt"/>
      <line num="203" count="0" type="stmt"/>
      <line num="206" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="207" count="0" type="stmt"/>
      <line num="211" count="6" type="stmt"/>
      <line num="213" count="0" type="stmt"/>
      <line num="215" count="0" type="stmt"/>
      <line num="216" count="0" type="stmt"/>
      <line num="217" count="0" type="stmt"/>
      <line num="218" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="219" count="0" type="stmt"/>
      <line num="223" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="224" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="225" count="0" type="stmt"/>
      <line num="226" count="0" type="stmt"/>
      <line num="228" count="0" type="stmt"/>
      <line num="229" count="0" type="stmt"/>
      <line num="231" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="232" count="0" type="stmt"/>
      <line num="235" count="0" type="stmt"/>
      <line num="236" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="237" count="0" type="stmt"/>
      <line num="238" count="0" type="stmt"/>
      <line num="243" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="244" count="0" type="stmt"/>
      <line num="245" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="246" count="0" type="stmt"/>
      <line num="248" count="0" type="stmt"/>
      <line num="252" count="6" type="stmt"/>
      <line num="258" count="0" type="stmt"/>
      <line num="260" count="0" type="stmt"/>
      <line num="261" count="0" type="stmt"/>
      <line num="262" count="0" type="stmt"/>
      <line num="263" count="0" type="stmt"/>
      <line num="264" count="0" type="stmt"/>
      <line num="266" count="0" type="stmt"/>
      <line num="267" count="0" type="stmt"/>
      <line num="270" count="0" type="stmt"/>
      <line num="273" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="274" count="0" type="stmt"/>
      <line num="276" count="0" type="stmt"/>
      <line num="280" count="6" type="stmt"/>
    </file>
    <file name="common.ts" path="/Users/<USER>/project/fastrx/src/common.ts">
      <metrics statements="137" coveredstatements="53" conditionals="30" coveredconditionals="3" methods="63" coveredmethods="25"/>
      <line num="1" count="6" type="stmt"/>
      <line num="2" count="16" type="stmt"/>
      <line num="3" count="6" type="stmt"/>
      <line num="4" count="6" type="stmt"/>
      <line num="5" count="0" type="stmt"/>
      <line num="8" count="12" type="stmt"/>
      <line num="32" count="6" type="stmt"/>
      <line num="36" count="6" type="stmt"/>
      <line num="42" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="48" count="0" type="stmt"/>
      <line num="49" count="0" type="stmt"/>
      <line num="50" count="0" type="stmt"/>
      <line num="51" count="0" type="stmt"/>
      <line num="60" count="6" type="stmt"/>
      <line num="62" count="19" type="stmt"/>
      <line num="63" count="19" type="stmt"/>
      <line num="67" count="0" type="stmt"/>
      <line num="70" count="0" type="stmt"/>
      <line num="73" count="15" type="stmt"/>
      <line num="76" count="19" type="stmt"/>
      <line num="77" count="19" type="stmt"/>
      <line num="78" count="19" type="stmt"/>
      <line num="79" count="19" type="stmt"/>
      <line num="80" count="19" type="stmt"/>
      <line num="81" count="19" type="stmt"/>
      <line num="82" count="19" type="stmt"/>
      <line num="85" count="12" type="cond" truecount="1" falsecount="1"/>
      <line num="86" count="0" type="stmt"/>
      <line num="88" count="12" type="stmt"/>
      <line num="89" count="12" type="stmt"/>
      <line num="92" count="0" type="stmt"/>
      <line num="95" count="19" type="stmt"/>
      <line num="96" count="19" type="stmt"/>
      <line num="99" count="17" type="stmt"/>
      <line num="102" count="1" type="stmt"/>
      <line num="105" count="0" type="stmt"/>
      <line num="107" count="0" type="stmt"/>
      <line num="109" count="0" type="stmt"/>
      <line num="111" count="0" type="stmt"/>
      <line num="113" count="0" type="stmt"/>
      <line num="115" count="0" type="stmt"/>
      <line num="119" count="0" type="stmt"/>
      <line num="123" count="4" type="stmt"/>
      <line num="127" count="0" type="stmt"/>
      <line num="131" count="6" type="stmt"/>
      <line num="132" count="15" type="stmt"/>
      <line num="133" count="15" type="stmt"/>
      <line num="134" count="15" type="stmt"/>
      <line num="137" count="3" type="stmt"/>
      <line num="140" count="6" type="stmt"/>
      <line num="143" count="0" type="stmt"/>
      <line num="146" count="6" type="stmt"/>
      <line num="147" count="4" type="stmt"/>
      <line num="148" count="4" type="cond" truecount="0" falsecount="3"/>
      <line num="149" count="4" type="stmt"/>
      <line num="150" count="4" type="cond" truecount="1" falsecount="1"/>
      <line num="151" count="0" type="stmt"/>
      <line num="152" count="0" type="stmt"/>
      <line num="153" count="0" type="stmt"/>
      <line num="155" count="0" type="stmt"/>
      <line num="156" count="0" type="stmt"/>
      <line num="157" count="0" type="stmt"/>
      <line num="158" count="0" type="stmt"/>
      <line num="159" count="0" type="stmt"/>
      <line num="160" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="161" count="0" type="stmt"/>
      <line num="163" count="0" type="stmt"/>
      <line num="164" count="0" type="stmt"/>
      <line num="165" count="0" type="stmt"/>
      <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="169" count="0" type="stmt"/>
      <line num="171" count="0" type="stmt"/>
      <line num="172" count="0" type="stmt"/>
      <line num="173" count="0" type="stmt"/>
      <line num="174" count="0" type="stmt"/>
      <line num="177" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="178" count="0" type="stmt"/>
      <line num="180" count="0" type="stmt"/>
      <line num="181" count="0" type="stmt"/>
      <line num="182" count="0" type="stmt"/>
      <line num="183" count="0" type="stmt"/>
      <line num="187" count="4" type="stmt"/>
      <line num="191" count="8" type="stmt"/>
      <line num="194" count="4" type="stmt"/>
      <line num="195" count="4" type="stmt"/>
      <line num="198" count="0" type="stmt"/>
      <line num="199" count="0" type="stmt"/>
      <line num="215" count="6" type="stmt"/>
      <line num="216" count="11" type="stmt"/>
      <line num="218" count="6" type="stmt"/>
      <line num="222" count="12" type="cond" truecount="0" falsecount="1"/>
      <line num="223" count="0" type="stmt"/>
      <line num="229" count="0" type="stmt"/>
      <line num="230" count="0" type="stmt"/>
      <line num="231" count="0" type="stmt"/>
      <line num="232" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="233" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="234" count="0" type="stmt"/>
      <line num="240" count="0" type="stmt"/>
      <line num="242" count="12" type="stmt"/>
      <line num="244" count="6" type="stmt"/>
      <line num="245" count="228" type="stmt"/>
      <line num="246" count="7" type="stmt"/>
      <line num="247" count="7" type="cond" truecount="1" falsecount="1"/>
      <line num="248" count="0" type="stmt"/>
      <line num="249" count="0" type="stmt"/>
      <line num="250" count="0" type="stmt"/>
      <line num="251" count="0" type="stmt"/>
      <line num="253" count="0" type="stmt"/>
      <line num="254" count="0" type="stmt"/>
      <line num="255" count="0" type="stmt"/>
      <line num="257" count="7" type="stmt"/>
      <line num="264" count="0" type="stmt"/>
      <line num="267" count="0" type="stmt"/>
      <line num="268" count="0" type="stmt"/>
      <line num="269" count="0" type="stmt"/>
      <line num="270" count="0" type="stmt"/>
      <line num="271" count="0" type="stmt"/>
      <line num="275" count="0" type="stmt"/>
      <line num="276" count="0" type="stmt"/>
      <line num="279" count="0" type="stmt"/>
      <line num="280" count="0" type="stmt"/>
      <line num="283" count="0" type="stmt"/>
      <line num="284" count="0" type="stmt"/>
      <line num="292" count="6" type="stmt"/>
      <line num="294" count="0" type="stmt"/>
      <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="304" count="0" type="stmt"/>
      <line num="311" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="314" count="0" type="stmt"/>
      <line num="317" count="0" type="stmt"/>
      <line num="324" count="0" type="stmt"/>
      <line num="327" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="328" count="0" type="stmt"/>
      <line num="331" count="6" type="stmt"/>
      <line num="332" count="0" type="stmt"/>
      <line num="333" count="0" type="stmt"/>
    </file>
    <file name="filtering.ts" path="/Users/<USER>/project/fastrx/src/filtering.ts">
      <metrics statements="149" coveredstatements="24" conditionals="33" coveredconditionals="0" methods="51" coveredmethods="0"/>
      <line num="1" count="6" type="stmt"/>
      <line num="2" count="6" type="stmt"/>
      <line num="3" count="6" type="stmt"/>
      <line num="5" count="0" type="stmt"/>
      <line num="6" count="0" type="stmt"/>
      <line num="9" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="10" count="0" type="stmt"/>
      <line num="14" count="6" type="stmt"/>
      <line num="19" count="6" type="stmt"/>
      <line num="21" count="0" type="stmt"/>
      <line num="22" count="0" type="stmt"/>
      <line num="25" count="0" type="stmt"/>
      <line num="26" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="27" count="0" type="stmt"/>
      <line num="31" count="6" type="stmt"/>
      <line num="34" count="0" type="stmt"/>
      <line num="35" count="0" type="stmt"/>
      <line num="36" count="0" type="stmt"/>
      <line num="37" count="0" type="stmt"/>
      <line num="38" count="0" type="stmt"/>
      <line num="41" count="6" type="stmt"/>
      <line num="44" count="0" type="stmt"/>
      <line num="45" count="0" type="stmt"/>
      <line num="48" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="49" count="0" type="stmt"/>
      <line num="51" count="0" type="stmt"/>
      <line num="55" count="6" type="stmt"/>
      <line num="57" count="6" type="stmt"/>
      <line num="58" count="0" type="stmt"/>
      <line num="59" count="0" type="stmt"/>
      <line num="60" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="61" count="0" type="stmt"/>
      <line num="65" count="0" type="stmt"/>
      <line num="66" count="0" type="stmt"/>
      <line num="69" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="70" count="0" type="stmt"/>
      <line num="74" count="6" type="stmt"/>
      <line num="77" count="0" type="stmt"/>
      <line num="78" count="0" type="stmt"/>
      <line num="79" count="0" type="stmt"/>
      <line num="80" count="0" type="stmt"/>
      <line num="81" count="0" type="stmt"/>
      <line num="82" count="0" type="stmt"/>
      <line num="84" count="0" type="stmt"/>
      <line num="85" count="0" type="stmt"/>
      <line num="88" count="6" type="stmt"/>
      <line num="91" count="0" type="stmt"/>
      <line num="92" count="0" type="stmt"/>
      <line num="95" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="96" count="0" type="stmt"/>
      <line num="97" count="0" type="stmt"/>
      <line num="101" count="6" type="stmt"/>
      <line num="102" count="6" type="stmt"/>
      <line num="108" count="0" type="stmt"/>
      <line num="109" count="0" type="stmt"/>
      <line num="112" count="0" type="stmt"/>
      <line num="113" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="116" count="0" type="stmt"/>
      <line num="117" count="0" type="stmt"/>
      <line num="120" count="0" type="stmt"/>
      <line num="121" count="0" type="stmt"/>
      <line num="124" count="0" type="stmt"/>
      <line num="127" count="0" type="stmt"/>
      <line num="128" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="129" count="0" type="stmt"/>
      <line num="134" count="0" type="stmt"/>
      <line num="135" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="136" count="0" type="stmt"/>
      <line num="137" count="0" type="stmt"/>
      <line num="140" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="141" count="0" type="stmt"/>
      <line num="143" count="0" type="stmt"/>
      <line num="147" count="0" type="stmt"/>
      <line num="148" count="0" type="stmt"/>
      <line num="149" count="0" type="stmt"/>
      <line num="152" count="6" type="stmt"/>
      <line num="153" count="6" type="stmt"/>
      <line num="157" count="6" type="stmt"/>
      <line num="161" count="0" type="stmt"/>
      <line num="164" count="0" type="stmt"/>
      <line num="165" count="0" type="stmt"/>
      <line num="169" count="0" type="stmt"/>
      <line num="170" count="0" type="stmt"/>
      <line num="171" count="0" type="stmt"/>
      <line num="172" count="0" type="stmt"/>
      <line num="175" count="0" type="stmt"/>
      <line num="176" count="0" type="stmt"/>
      <line num="177" count="0" type="stmt"/>
      <line num="178" count="0" type="stmt"/>
      <line num="181" count="0" type="stmt"/>
      <line num="182" count="0" type="stmt"/>
      <line num="186" count="6" type="stmt"/>
      <line num="188" count="6" type="stmt"/>
      <line num="190" count="0" type="stmt"/>
      <line num="191" count="0" type="stmt"/>
      <line num="194" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="195" count="0" type="stmt"/>
      <line num="196" count="0" type="stmt"/>
      <line num="200" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="201" count="0" type="stmt"/>
      <line num="202" count="0" type="stmt"/>
      <line num="203" count="0" type="stmt"/>
      <line num="204" count="0" type="stmt"/>
      <line num="208" count="6" type="stmt"/>
      <line num="209" count="6" type="stmt"/>
      <line num="211" count="0" type="stmt"/>
      <line num="212" count="0" type="stmt"/>
      <line num="213" count="0" type="stmt"/>
      <line num="216" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="217" count="0" type="stmt"/>
      <line num="218" count="0" type="stmt"/>
      <line num="220" count="0" type="stmt"/>
      <line num="224" count="6" type="stmt"/>
      <line num="226" count="0" type="stmt"/>
      <line num="227" count="0" type="stmt"/>
      <line num="228" count="0" type="stmt"/>
      <line num="231" count="0" type="cond" truecount="0" falsecount="3"/>
      <line num="232" count="0" type="stmt"/>
      <line num="233" count="0" type="stmt"/>
      <line num="237" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="238" count="0" type="stmt"/>
      <line num="239" count="0" type="stmt"/>
      <line num="240" count="0" type="stmt"/>
      <line num="241" count="0" type="stmt"/>
      <line num="245" count="6" type="stmt"/>
      <line num="247" count="0" type="stmt"/>
      <line num="248" count="0" type="stmt"/>
      <line num="249" count="0" type="stmt"/>
      <line num="252" count="0" type="cond" truecount="0" falsecount="3"/>
      <line num="253" count="0" type="stmt"/>
      <line num="257" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="258" count="0" type="stmt"/>
      <line num="259" count="0" type="stmt"/>
      <line num="260" count="0" type="stmt"/>
      <line num="261" count="0" type="stmt"/>
      <line num="265" count="6" type="stmt"/>
      <line num="269" count="0" type="stmt"/>
      <line num="270" count="0" type="stmt"/>
      <line num="271" count="0" type="stmt"/>
      <line num="274" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="275" count="0" type="stmt"/>
      <line num="276" count="0" type="stmt"/>
      <line num="278" count="0" type="stmt"/>
      <line num="282" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="283" count="0" type="stmt"/>
      <line num="284" count="0" type="stmt"/>
      <line num="285" count="0" type="stmt"/>
      <line num="286" count="0" type="stmt"/>
      <line num="290" count="6" type="stmt"/>
    </file>
    <file name="index.ts" path="/Users/<USER>/project/fastrx/src/index.ts">
      <metrics statements="7" coveredstatements="7" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <line num="1" count="6" type="stmt"/>
      <line num="2" count="6" type="stmt"/>
      <line num="3" count="6" type="stmt"/>
      <line num="4" count="6" type="stmt"/>
      <line num="5" count="6" type="stmt"/>
      <line num="6" count="6" type="stmt"/>
      <line num="7" count="6" type="stmt"/>
    </file>
    <file name="mathematical.ts" path="/Users/<USER>/project/fastrx/src/mathematical.ts">
      <metrics statements="19" coveredstatements="6" conditionals="4" coveredconditionals="0" methods="10" coveredmethods="0"/>
      <line num="1" count="6" type="stmt"/>
      <line num="5" count="0" type="stmt"/>
      <line num="6" count="0" type="stmt"/>
      <line num="7" count="0" type="stmt"/>
      <line num="8" count="0" type="stmt"/>
      <line num="9" count="0" type="stmt"/>
      <line num="11" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="12" count="0" type="stmt"/>
      <line num="13" count="0" type="stmt"/>
      <line num="14" count="0" type="stmt"/>
      <line num="15" count="0" type="stmt"/>
      <line num="18" count="0" type="stmt"/>
      <line num="19" count="0" type="stmt"/>
      <line num="23" count="0" type="stmt"/>
      <line num="26" count="6" type="stmt"/>
      <line num="28" count="6" type="cond" truecount="0" falsecount="2"/>
      <line num="29" count="6" type="stmt"/>
      <line num="30" count="6" type="stmt"/>
      <line num="31" count="6" type="stmt"/>
    </file>
    <file name="producer.ts" path="/Users/<USER>/project/fastrx/src/producer.ts">
      <metrics statements="125" coveredstatements="38" conditionals="27" coveredconditionals="3" methods="72" coveredmethods="10"/>
      <line num="1" count="6" type="stmt"/>
      <line num="2" count="6" type="stmt"/>
      <line num="4" count="6" type="stmt"/>
      <line num="5" count="0" type="stmt"/>
      <line num="6" count="0" type="stmt"/>
      <line num="7" count="0" type="stmt"/>
      <line num="8" count="0" type="stmt"/>
      <line num="9" count="0" type="stmt"/>
      <line num="10" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="12" count="0" type="stmt"/>
      <line num="13" count="0" type="stmt"/>
      <line num="14" count="0" type="stmt"/>
      <line num="15" count="0" type="stmt"/>
      <line num="17" count="6" type="stmt"/>
      <line num="18" count="0" type="stmt"/>
      <line num="20" count="10" type="stmt"/>
      <line num="21" count="10" type="stmt"/>
      <line num="24" count="10" type="stmt"/>
      <line num="25" count="10" type="cond" truecount="2" falsecount="0"/>
      <line num="26" count="7" type="stmt"/>
      <line num="28" count="10" type="stmt"/>
      <line num="31" count="6" type="stmt"/>
      <line num="32" count="10" type="stmt"/>
      <line num="35" count="6" type="stmt"/>
      <line num="36" count="0" type="stmt"/>
      <line num="39" count="6" type="stmt"/>
      <line num="40" count="0" type="stmt"/>
      <line num="41" count="0" type="stmt"/>
      <line num="42" count="0" type="stmt"/>
      <line num="43" count="0" type="stmt"/>
      <line num="44" count="0" type="stmt"/>
      <line num="48" count="6" type="stmt"/>
      <line num="49" count="2" type="stmt"/>
      <line num="50" count="2" type="stmt"/>
      <line num="51" count="2" type="stmt"/>
      <line num="52" count="1" type="stmt"/>
      <line num="53" count="1" type="stmt"/>
      <line num="54" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="55" count="0" type="stmt"/>
      <line num="56" count="0" type="stmt"/>
      <line num="58" count="1" type="stmt"/>
      <line num="61" count="2" type="stmt"/>
      <line num="62" count="2" type="stmt"/>
      <line num="66" count="0" type="stmt"/>
      <line num="67" count="0" type="stmt"/>
      <line num="68" count="0" type="stmt"/>
      <line num="69" count="0" type="stmt"/>
      <line num="72" count="6" type="stmt"/>
      <line num="73" count="0" type="stmt"/>
      <line num="76" count="6" type="stmt"/>
      <line num="77" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="78" count="0" type="stmt"/>
      <line num="79" count="0" type="stmt"/>
      <line num="80" count="0" type="stmt"/>
      <line num="81" count="0" type="cond" truecount="0" falsecount="4"/>
      <line num="82" count="0" type="stmt"/>
      <line num="83" count="0" type="stmt"/>
      <line num="84" count="0" type="stmt"/>
      <line num="85" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="86" count="0" type="stmt"/>
      <line num="87" count="0" type="stmt"/>
      <line num="88" count="0" type="stmt"/>
      <line num="90" count="0" type="stmt"/>
      <line num="93" count="6" type="stmt"/>
      <line num="94" count="0" type="stmt"/>
      <line num="95" count="0" type="stmt"/>
      <line num="98" count="6" type="stmt"/>
      <line num="99" count="0" type="stmt"/>
      <line num="101" count="6" type="stmt"/>
      <line num="102" count="0" type="stmt"/>
      <line num="103" count="0" type="stmt"/>
      <line num="104" count="0" type="stmt"/>
      <line num="105" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="106" count="0" type="stmt"/>
      <line num="108" count="0" type="stmt"/>
      <line num="110" count="0" type="stmt"/>
      <line num="114" count="6" type="stmt"/>
      <line num="115" count="0" type="stmt"/>
      <line num="116" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="117" count="0" type="stmt"/>
      <line num="118" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="119" count="0" type="stmt"/>
      <line num="120" count="0" type="stmt"/>
      <line num="122" count="0" type="stmt"/>
      <line num="123" count="0" type="stmt"/>
      <line num="126" count="0" type="stmt"/>
      <line num="127" count="0" type="stmt"/>
      <line num="130" count="6" type="stmt"/>
      <line num="131" count="0" type="stmt"/>
      <line num="132" count="0" type="stmt"/>
      <line num="133" count="0" type="stmt"/>
      <line num="135" count="0" type="stmt"/>
      <line num="136" count="0" type="stmt"/>
      <line num="138" count="0" type="stmt"/>
      <line num="141" count="0" type="stmt"/>
      <line num="144" count="0" type="stmt"/>
      <line num="146" count="0" type="stmt"/>
      <line num="149" count="6" type="stmt"/>
      <line num="150" count="0" type="stmt"/>
      <line num="151" count="0" type="stmt"/>
      <line num="152" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="153" count="0" type="stmt"/>
      <line num="154" count="0" type="stmt"/>
      <line num="157" count="0" type="stmt"/>
      <line num="160" count="6" type="stmt"/>
      <line num="162" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="163" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="164" count="0" type="stmt"/>
      <line num="165" count="0" type="stmt"/>
      <line num="169" count="6" type="stmt"/>
      <line num="171" count="0" type="stmt"/>
      <line num="172" count="0" type="stmt"/>
      <line num="173" count="0" type="stmt"/>
      <line num="175" count="0" type="stmt"/>
      <line num="178" count="6" type="stmt"/>
      <line num="179" count="0" type="stmt"/>
      <line num="180" count="0" type="stmt"/>
      <line num="181" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="183" count="0" type="stmt"/>
      <line num="186" count="6" type="stmt"/>
      <line num="187" count="0" type="stmt"/>
      <line num="189" count="6" type="stmt"/>
      <line num="190" count="0" type="stmt"/>
      <line num="192" count="6" type="stmt"/>
      <line num="193" count="0" type="stmt"/>
    </file>
    <file name="transformation.ts" path="/Users/<USER>/project/fastrx/src/transformation.ts">
      <metrics statements="154" coveredstatements="44" conditionals="22" coveredconditionals="1" methods="61" coveredmethods="12"/>
      <line num="1" count="6" type="stmt"/>
      <line num="2" count="6" type="stmt"/>
      <line num="7" count="0" type="stmt"/>
      <line num="8" count="0" type="stmt"/>
      <line num="9" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="10" count="0" type="stmt"/>
      <line num="11" count="0" type="stmt"/>
      <line num="12" count="0" type="stmt"/>
      <line num="13" count="0" type="stmt"/>
      <line num="16" count="0" type="stmt"/>
      <line num="20" count="0" type="stmt"/>
      <line num="23" count="6" type="stmt"/>
      <line num="25" count="0" type="stmt"/>
      <line num="28" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="29" count="0" type="stmt"/>
      <line num="31" count="0" type="stmt"/>
      <line num="33" count="0" type="stmt"/>
      <line num="36" count="6" type="stmt"/>
      <line num="38" count="3" type="stmt"/>
      <line num="39" count="3" type="stmt"/>
      <line num="42" count="3" type="stmt"/>
      <line num="45" count="6" type="stmt"/>
      <line num="46" count="6" type="stmt"/>
      <line num="54" count="8" type="stmt"/>
      <line num="55" count="8" type="stmt"/>
      <line num="58" count="0" type="stmt"/>
      <line num="59" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="60" count="0" type="stmt"/>
      <line num="62" count="0" type="stmt"/>
      <line num="67" count="0" type="stmt"/>
      <line num="68" count="0" type="stmt"/>
      <line num="75" count="4" type="stmt"/>
      <line num="76" count="4" type="stmt"/>
      <line num="77" count="4" type="stmt"/>
      <line num="80" count="8" type="stmt"/>
      <line num="81" count="8" type="stmt"/>
      <line num="82" count="8" type="stmt"/>
      <line num="83" count="8" type="stmt"/>
      <line num="88" count="0" type="stmt"/>
      <line num="89" count="0" type="stmt"/>
      <line num="97" count="0" type="stmt"/>
      <line num="98" count="0" type="stmt"/>
      <line num="99" count="0" type="stmt"/>
      <line num="100" count="0" type="stmt"/>
      <line num="105" count="6" type="stmt"/>
      <line num="108" count="24" type="stmt"/>
      <line num="111" count="6" type="stmt"/>
      <line num="114" count="0" type="stmt"/>
      <line num="115" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="116" count="0" type="stmt"/>
      <line num="118" count="0" type="stmt"/>
      <line num="119" count="0" type="stmt"/>
      <line num="125" count="0" type="stmt"/>
      <line num="126" count="0" type="stmt"/>
      <line num="128" count="0" type="stmt"/>
      <line num="129" count="0" type="stmt"/>
      <line num="132" count="0" type="stmt"/>
      <line num="133" count="0" type="stmt"/>
      <line num="134" count="0" type="cond" truecount="0" falsecount="3"/>
      <line num="136" count="0" type="stmt"/>
      <line num="140" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="142" count="0" type="stmt"/>
      <line num="143" count="0" type="stmt"/>
      <line num="147" count="6" type="stmt"/>
      <line num="148" count="6" type="stmt"/>
      <line num="152" count="0" type="stmt"/>
      <line num="153" count="0" type="stmt"/>
      <line num="154" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="155" count="0" type="stmt"/>
      <line num="161" count="0" type="stmt"/>
      <line num="163" count="0" type="stmt"/>
      <line num="164" count="0" type="stmt"/>
      <line num="168" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="169" count="0" type="stmt"/>
      <line num="170" count="0" type="stmt"/>
      <line num="173" count="6" type="stmt"/>
      <line num="174" count="6" type="stmt"/>
      <line num="178" count="0" type="stmt"/>
      <line num="179" count="0" type="stmt"/>
      <line num="184" count="0" type="stmt"/>
      <line num="185" count="0" type="stmt"/>
      <line num="188" count="6" type="stmt"/>
      <line num="189" count="6" type="stmt"/>
      <line num="193" count="0" type="stmt"/>
      <line num="194" count="0" type="stmt"/>
      <line num="195" count="0" type="stmt"/>
      <line num="198" count="0" type="stmt"/>
      <line num="199" count="0" type="stmt"/>
      <line num="200" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="201" count="0" type="stmt"/>
      <line num="202" count="0" type="stmt"/>
      <line num="203" count="0" type="stmt"/>
      <line num="204" count="0" type="stmt"/>
      <line num="206" count="0" type="stmt"/>
      <line num="209" count="0" type="stmt"/>
      <line num="210" count="0" type="stmt"/>
      <line num="213" count="0" type="stmt"/>
      <line num="214" count="0" type="stmt"/>
      <line num="217" count="6" type="stmt"/>
      <line num="220" count="0" type="stmt"/>
      <line num="222" count="0" type="stmt"/>
      <line num="223" count="0" type="stmt"/>
      <line num="226" count="6" type="stmt"/>
      <line num="229" count="0" type="stmt"/>
      <line num="230" count="0" type="stmt"/>
      <line num="231" count="0" type="stmt"/>
      <line num="232" count="0" type="stmt"/>
      <line num="234" count="0" type="stmt"/>
      <line num="235" count="0" type="stmt"/>
      <line num="238" count="0" type="stmt"/>
      <line num="241" count="0" type="stmt"/>
      <line num="242" count="0" type="stmt"/>
      <line num="245" count="0" type="stmt"/>
      <line num="246" count="0" type="stmt"/>
      <line num="250" count="6" type="stmt"/>
      <line num="254" count="0" type="stmt"/>
      <line num="257" count="0" type="stmt"/>
      <line num="258" count="0" type="stmt"/>
      <line num="261" count="0" type="stmt"/>
      <line num="262" count="0" type="stmt"/>
      <line num="265" count="0" type="stmt"/>
      <line num="266" count="0" type="stmt"/>
      <line num="267" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="268" count="0" type="stmt"/>
      <line num="269" count="0" type="stmt"/>
      <line num="270" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="271" count="0" type="stmt"/>
      <line num="278" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="279" count="0" type="stmt"/>
      <line num="281" count="0" type="stmt"/>
      <line num="284" count="0" type="stmt"/>
      <line num="287" count="6" type="stmt"/>
      <line num="289" count="0" type="stmt"/>
      <line num="290" count="0" type="stmt"/>
      <line num="293" count="0" type="stmt"/>
      <line num="294" count="0" type="stmt"/>
      <line num="298" count="6" type="stmt"/>
      <line num="301" count="0" type="stmt"/>
      <line num="302" count="0" type="stmt"/>
      <line num="303" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="304" count="0" type="stmt"/>
      <line num="309" count="4" type="stmt"/>
      <line num="311" count="4" type="stmt"/>
      <line num="316" count="4" type="stmt"/>
      <line num="318" count="4" type="stmt"/>
      <line num="319" count="4" type="stmt"/>
      <line num="324" count="4" type="stmt"/>
      <line num="326" count="4" type="stmt"/>
      <line num="330" count="8" type="stmt"/>
      <line num="331" count="8" type="stmt"/>
      <line num="336" count="4" type="cond" truecount="1" falsecount="1"/>
      <line num="337" count="4" type="stmt"/>
      <line num="338" count="0" type="stmt"/>
      <line num="342" count="6" type="stmt"/>
    </file>
    <file name="utils.ts" path="/Users/<USER>/project/fastrx/src/utils.ts">
      <metrics statements="48" coveredstatements="8" conditionals="15" coveredconditionals="0" methods="26" coveredmethods="2"/>
      <line num="1" count="6" type="stmt"/>
      <line num="3" count="6" type="stmt"/>
      <line num="4" count="0" type="stmt"/>
      <line num="6" count="0" type="stmt"/>
      <line num="9" count="6" type="stmt"/>
      <line num="11" count="0" type="stmt"/>
      <line num="13" count="0" type="stmt"/>
      <line num="16" count="0" type="stmt"/>
      <line num="24" count="6" type="stmt"/>
      <line num="25" count="6" type="cond" truecount="0" falsecount="3"/>
      <line num="30" count="0" type="stmt"/>
      <line num="31" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="32" count="0" type="stmt"/>
      <line num="34" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="35" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="36" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="41" count="6" type="stmt"/>
      <line num="44" count="0" type="stmt"/>
      <line num="45" count="0" type="stmt"/>
      <line num="46" count="0" type="stmt"/>
      <line num="49" count="0" type="stmt"/>
      <line num="50" count="0" type="stmt"/>
      <line num="51" count="0" type="stmt"/>
      <line num="54" count="0" type="stmt"/>
      <line num="55" count="0" type="stmt"/>
      <line num="58" count="6" type="stmt"/>
      <line num="60" count="6" type="cond" truecount="0" falsecount="1"/>
      <line num="61" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="62" count="0" type="stmt"/>
      <line num="63" count="0" type="stmt"/>
      <line num="64" count="0" type="stmt"/>
      <line num="65" count="0" type="stmt"/>
      <line num="66" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="67" count="0" type="stmt"/>
      <line num="69" count="0" type="stmt"/>
      <line num="72" count="0" type="stmt"/>
      <line num="73" count="0" type="stmt"/>
      <line num="75" count="0" type="stmt"/>
      <line num="76" count="0" type="stmt"/>
      <line num="77" count="0" type="stmt"/>
      <line num="79" count="0" type="stmt"/>
      <line num="80" count="0" type="stmt"/>
      <line num="81" count="0" type="stmt"/>
      <line num="82" count="0" type="stmt"/>
      <line num="83" count="0" type="cond" truecount="0" falsecount="2"/>
      <line num="84" count="0" type="stmt"/>
      <line num="86" count="0" type="stmt"/>
      <line num="89" count="0" type="stmt"/>
    </file>
  </project>
</coverage>
