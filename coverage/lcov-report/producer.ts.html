
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for producer.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="index.html">All files</a> producer.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.33% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>44/150</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">11.11% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>3/27</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">15.27% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>11/72</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">31.2% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>39/125</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">18x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-yes">3x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">6x</span>
<span class="cline-any cline-yes">4x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { share } from "./combination";
import { ISink, Observable, nothing, Observer, create, EventHandler, EventDispachter } from "./common";
export type Subject&lt;T&gt; = Observable&lt;T&gt; &amp; Observer&lt;T&gt;;
export function <span class="fstat-no" title="function not covered" >subject&lt;</span>T&gt;(source?: Observable&lt;T&gt;) {
  const args = <span class="cstat-no" title="statement not covered" >arguments;</span>
  const observable: Subject&lt;T&gt; = <span class="cstat-no" title="statement not covered" >share&lt;T&gt;()(create(<span class="fstat-no" title="function not covered" >(s</span>ink: ISink&lt;T&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    observable.next = <span class="fstat-no" title="function not covered" >(d</span>ata: T) =&gt; <span class="cstat-no" title="statement not covered" >sink.next(data);</span></span>
<span class="cstat-no" title="statement not covered" >    observable.complete = <span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >sink.complete();</span></span>
<span class="cstat-no" title="statement not covered" >    observable.error = <span class="fstat-no" title="function not covered" >(e</span>rr: any) =&gt; <span class="cstat-no" title="statement not covered" >sink.error(err);</span></span>
<span class="cstat-no" title="statement not covered" >    source &amp;&amp; sink.subscribe(source);</span>
  }, "subject", args)) as Subject&lt;T&gt;;
<span class="cstat-no" title="statement not covered" >  observable.next = nothing;</span>
<span class="cstat-no" title="statement not covered" >  observable.complete = nothing;</span>
<span class="cstat-no" title="statement not covered" >  observable.error = nothing;</span>
<span class="cstat-no" title="statement not covered" >  return observable;</span>
};
export function <span class="fstat-no" title="function not covered" >defer&lt;</span>T&gt;(f: () =&gt; Observable&lt;T&gt;): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >sink </span>=&gt; <span class="cstat-no" title="statement not covered" >sink.subscribe(f()),</span> "defer", arguments);</span>
}
const asap = &lt;T&gt;(f: (sink: ISink&lt;T&gt;) =&gt; void) =&gt; (sink: ISink&lt;T&gt;) =&gt; {
  setTimeout(() =&gt; f(sink));
};
&nbsp;
const _fromArray = &lt;T&gt;(data: ArrayLike&lt;T&gt;) =&gt; asap((sink: ISink&lt;T&gt;) =&gt; {
  for (let i = 0; !sink.disposed &amp;&amp; i &lt; data.length; i++) {
    sink.next(data[i]);
  }
  sink.complete();
});
&nbsp;
export function of&lt;T&gt;(...data: T[]) {
  return create(_fromArray(data), "of", arguments);
}
&nbsp;
export function <span class="fstat-no" title="function not covered" >fromArray&lt;</span>T&gt;(data: ArrayLike&lt;T&gt;): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return create(_fromArray(data), "fromArray", arguments);</span>
}
&nbsp;
export function <span class="fstat-no" title="function not covered" >interval(</span>period: number): Observable&lt;number&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >(s</span>ink: ISink&lt;number&gt;) =&gt; {</span>
    let i = <span class="cstat-no" title="statement not covered" >0;</span>
    const id = <span class="cstat-no" title="statement not covered" >setInterval(<span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >sink.next(i++),</span> period);</span>
<span class="cstat-no" title="statement not covered" >    sink.defer(<span class="fstat-no" title="function not covered" >() =</span>&gt; { <span class="cstat-no" title="statement not covered" >clearInterval(id); </span>});</span>
<span class="cstat-no" title="statement not covered" >    return "interval";</span>
  }, "interval", arguments);
}
&nbsp;
export function timer(delay: number, period?: number): Observable&lt;number&gt; {
  return create((sink: ISink&lt;number&gt;) =&gt; {
    let i = 0;
    const id = setTimeout(() =&gt; {
      sink.removeDefer(deferF);
      sink.next(i++);
      <span class="missing-if-branch" title="if path not taken" >I</span>if (period) {
        const id = <span class="cstat-no" title="statement not covered" >setInterval(<span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >sink.next(i++),</span> period);</span>
<span class="cstat-no" title="statement not covered" >        sink.defer(<span class="fstat-no" title="function not covered" >() =</span>&gt; { <span class="cstat-no" title="statement not covered" >clearInterval(id); </span>});</span>
      } else {
        sink.complete();
      }
    }, delay);
    const deferF = <span class="fstat-no" title="function not covered" >() =</span>&gt; { <span class="cstat-no" title="statement not covered" >clearTimeout(id); </span>};
    sink.defer(deferF);
  }, "timer", arguments);
};
function <span class="fstat-no" title="function not covered" >_fromEventPattern&lt;</span>T&gt;(add: (n: EventHandler&lt;T&gt;) =&gt; void, remove: (n: EventHandler&lt;T&gt;) =&gt; void): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return <span class="fstat-no" title="function not covered" >(s</span>ink: ISink&lt;T&gt;) =&gt; {</span>
    const n: EventHandler&lt;T&gt; = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(d</span>) =&gt; <span class="cstat-no" title="statement not covered" >sink.next(d);</span></span>
<span class="cstat-no" title="statement not covered" >    sink.defer(<span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >remove(n))</span>;</span>
<span class="cstat-no" title="statement not covered" >    add(n);</span>
  };
}
export function <span class="fstat-no" title="function not covered" >fromEventPattern&lt;</span>T&gt;(add: (n: EventHandler&lt;T&gt;) =&gt; void, remove: (n: EventHandler&lt;T&gt;) =&gt; void): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return create(_fromEventPattern(add, remove), "fromEventPattern", arguments);</span>
};
&nbsp;
export function <span class="fstat-no" title="function not covered" >fromEvent&lt;</span>T, N&gt;(target: EventDispachter&lt;N, T&gt;, name: N) {
<span class="cstat-no" title="statement not covered" >  if ("on" in target &amp;&amp; "off" in target) {</span>
<span class="cstat-no" title="statement not covered" >    return create(_fromEventPattern&lt;T&gt;(</span>
<span class="fstat-no" title="function not covered" >      (h</span>) =&gt; <span class="cstat-no" title="statement not covered" >target.on(name, h),</span>
<span class="fstat-no" title="function not covered" >      (h</span>) =&gt; <span class="cstat-no" title="statement not covered" >target.off(name, h))</span>, "fromEvent", arguments);
  } else <span class="cstat-no" title="statement not covered" >if ("addListener" in target &amp;&amp; "removeListener" in target) {</span>
<span class="cstat-no" title="statement not covered" >    return create(_fromEventPattern&lt;T&gt;(</span>
<span class="fstat-no" title="function not covered" >      (h</span>) =&gt; <span class="cstat-no" title="statement not covered" >target.addListener(name, h),</span>
<span class="fstat-no" title="function not covered" >      (h</span>) =&gt; <span class="cstat-no" title="statement not covered" >target.removeListener(name, h))</span>, "fromEvent", arguments);
  } else <span class="cstat-no" title="statement not covered" >if ("addEventListener" in target) {</span>
<span class="cstat-no" title="statement not covered" >    return create(_fromEventPattern&lt;T&gt;(</span>
<span class="fstat-no" title="function not covered" >      (h</span>) =&gt; <span class="cstat-no" title="statement not covered" >target.addEventListener(name, h),</span>
<span class="fstat-no" title="function not covered" >      (h</span>) =&gt; <span class="cstat-no" title="statement not covered" >target.removeEventListener(name, h))</span>, "fromEvent", arguments);
  }
  else <span class="cstat-no" title="statement not covered" >throw 'target is not a EventDispachter';</span>
};
&nbsp;
export function <span class="fstat-no" title="function not covered" >fromPromise&lt;</span>T&gt;(promise: Promise&lt;T&gt;): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >(s</span>ink: ISink&lt;T&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    promise.then(sink.next.bind(sink), sink.error.bind(sink));</span>
  }, "fromPromise", arguments);
}
export function <span class="fstat-no" title="function not covered" >fromFetch(</span>input: RequestInfo, init?: RequestInit) {
<span class="cstat-no" title="statement not covered" >  return create(defer(<span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >fromPromise(fetch(input, init)))</span>, "fromFetch", arguments);</span>
}
export function <span class="fstat-no" title="function not covered" >fromIterable&lt;</span>T&gt;(source: Iterable&lt;T&gt;): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return create(asap(<span class="fstat-no" title="function not covered" >(s</span>ink: ISink&lt;T&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    try {</span>
<span class="cstat-no" title="statement not covered" >      for (const data of source) {</span>
<span class="cstat-no" title="statement not covered" >        <span class="missing-if-branch" title="if path not taken" >I</span>if (sink.disposed) <span class="cstat-no" title="statement not covered" >return;</span></span>
<span class="cstat-no" title="statement not covered" >        sink.next(data);</span>
      }
<span class="cstat-no" title="statement not covered" >      sink.complete();</span>
    } catch (err) {
<span class="cstat-no" title="statement not covered" >      sink.error(err);</span>
    }
  }), "fromIterable", arguments);
}
export function <span class="fstat-no" title="function not covered" >fromReader&lt;</span>T&gt;(source: ReadableStreamDefaultReader&lt;T&gt;): Observable&lt;T&gt; {
  const read = <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >async (s</span>ink: ISink&lt;T&gt;) =&gt;<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" > {</span></span></span>
<span class="cstat-no" title="statement not covered" >    <span class="missing-if-branch" title="if path not taken" >I</span>if (sink.disposed) <span class="cstat-no" title="statement not covered" >return;</span></span>
    const { done, value } = <span class="cstat-no" title="statement not covered" >await source.read();</span>
<span class="cstat-no" title="statement not covered" >    if (done) {</span>
<span class="cstat-no" title="statement not covered" >      sink.complete();</span>
<span class="cstat-no" title="statement not covered" >      return;</span>
    } else {
<span class="cstat-no" title="statement not covered" >      sink.next(value!);</span>
<span class="cstat-no" title="statement not covered" >      read(sink);</span>
    }
  };
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >(s</span>ink: ISink&lt;T&gt;) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    read(sink);</span>
  }, "fromReader", arguments);
}
export function <span class="fstat-no" title="function not covered" >fromReadableStream&lt;</span>T&gt;(source: ReadableStream&lt;T&gt;): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >(s</span>ink: ISink&lt;T&gt;) =&gt; {</span>
    const controller = <span class="cstat-no" title="statement not covered" >new AbortController();</span>
    const signal = <span class="cstat-no" title="statement not covered" >controller.signal;</span>
    //@ts-ignore
<span class="cstat-no" title="statement not covered" >    sink.defer(<span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >controller.abort('cancelled'))</span>;</span>
<span class="cstat-no" title="statement not covered" >    source.pipeTo(new WritableStream({</span>
<span class="fstat-no" title="function not covered" >      write(</span>chunk: T) {
<span class="cstat-no" title="statement not covered" >        sink.next(chunk);</span>
      },
<span class="fstat-no" title="function not covered" >      close(</span>) {
<span class="cstat-no" title="statement not covered" >        sink.complete();</span>
      },
<span class="fstat-no" title="function not covered" >      abort(</span>err) {
<span class="cstat-no" title="statement not covered" >        sink.error(err);</span>
      }
    }), { signal }).then(<span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >sink.complete(),</span> <span class="fstat-no" title="function not covered" >(e</span>rr) =&gt; <span class="cstat-no" title="statement not covered" >sink.error(err))</span>;
  }, "fromReadableStream", arguments);
}
export function <span class="fstat-no" title="function not covered" >fromAnimationFrame(</span>): Observable&lt;DOMHighResTimeStamp&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >(s</span>ink) =&gt; {</span>
    let id = <span class="cstat-no" title="statement not covered" >requestAnimationFrame(function <span class="fstat-no" title="function not covered" >next(</span>t: DOMHighResTimeStamp) {</span>
<span class="cstat-no" title="statement not covered" >      <span class="missing-if-branch" title="if path not taken" >I</span>if (!sink.disposed) {</span>
<span class="cstat-no" title="statement not covered" >        sink.next(t);</span>
<span class="cstat-no" title="statement not covered" >        id = requestAnimationFrame(next);</span>
      }
    });
<span class="cstat-no" title="statement not covered" >    sink.defer(<span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >cancelAnimationFrame(id))</span>;</span>
  }, "fromAnimationFrame", arguments);
}
export function <span class="fstat-no" title="function not covered" >range</span>
  (start: number, count: number): Observable&lt;number&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >(s</span>ink, pos = <span class="branch-0 cbranch-no" title="branch not covered" >start,</span> end = <span class="branch-0 cbranch-no" title="branch not covered" >count + start)</span> =&gt; {</span>
<span class="cstat-no" title="statement not covered" >    while (pos &lt; end &amp;&amp; !sink.disposed) <span class="cstat-no" title="statement not covered" >sink.next(pos++);</span></span>
<span class="cstat-no" title="statement not covered" >    sink.complete();</span>
<span class="cstat-no" title="statement not covered" >    return "range";</span>
  }, "range", arguments);
}
&nbsp;
export function <span class="fstat-no" title="function not covered" >bindCallback</span>
  &lt;T&gt;(call: Function, thisArg: any, ...args: any[]): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >(s</span>ink) =&gt; {</span>
    const inArgs = <span class="cstat-no" title="statement not covered" >args.concat(</span>
<span class="fstat-no" title="function not covered" >      (r</span>es: T) =&gt; (<span class="cstat-no" title="statement not covered" >sink.next(res), sink.complete())</span>
    );
<span class="cstat-no" title="statement not covered" >    call.apply(thisArg, inArgs);</span>
  }, "bindCallback", arguments);
}
export function <span class="fstat-no" title="function not covered" >bindNodeCallback&lt;</span>T&gt;(call: Function, thisArg: any, ...args: any[]): Observable&lt;T&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >(s</span>ink) =&gt; {</span>
    const inArgs = <span class="cstat-no" title="statement not covered" >args.concat(</span>
<span class="fstat-no" title="function not covered" >      (e</span>rr: Error, res: T) =&gt; <span class="cstat-no" title="statement not covered" >err ? (sink.error(err)) : (sink.next(res), sink.complete())</span>
    );
<span class="cstat-no" title="statement not covered" >    call.apply(thisArg, inArgs);</span>
  }, "bindNodeCallback", arguments);
}
export function <span class="fstat-no" title="function not covered" >never(</span>): Observable&lt;never&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >() =</span>&gt; { }, "never", arguments);</span>
};
export function <span class="fstat-no" title="function not covered" >throwError(</span>e: any): Observable&lt;never&gt; {
<span class="cstat-no" title="statement not covered" >  return create(<span class="fstat-no" title="function not covered" >sink </span>=&gt; <span class="cstat-no" title="statement not covered" >sink.error(e),</span> "throwError", arguments);</span>
}
export function empty(): Observable&lt;never&gt; {
  return create(sink =&gt; sink.complete(), "empty", arguments);
};
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-18T09:35:16.250Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    