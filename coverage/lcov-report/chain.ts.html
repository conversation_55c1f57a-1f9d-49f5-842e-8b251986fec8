
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for chain.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="index.html">All files</a> chain.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.43% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>13/28</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/9</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/12</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">57.14% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>12/21</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">// @ts-nocheck
import { Subscribe, tap, delay, timeout, catchError, groupBy, subscribe, toPromise } from './pipe';
import { Node } from './devtools';
import { Observable, Operator } from './common';
import * as producer from './producer';
import * as filtering from './filtering';
import * as mathematical from './mathematical';
import * as transformation from './transformation';
import * as combination from './combination';
const { zip, merge, race, concat, combineLatest, ...combinations } = combination;
const observables = { zip, merge, race, concat, combineLatest, ...producer };
const operators = { tap, delay, timeout, catchError, groupBy, ...combinations, ...filtering, ...mathematical, ...transformation };
&nbsp;
function <span class="fstat-no" title="function not covered" >inspect(</span>) {
<span class="cstat-no" title="statement not covered" >    return typeof window != 'undefined' &amp;&amp; window.__FASTRX_DEVTOOLS__;</span>
}
// (typeof operators)[keyof typeof operators] extends (...arg:any[])=&gt;Operator&lt;number,number&gt;?
type Operators&lt;T&gt; = {
    [Key in keyof typeof operators]: (typeof operators)[Key] extends (...arg: any[]) =&gt; Operator&lt;T, any&gt; ? (typeof operators)[Key] : never
};
const rxProxy = {
    get: <span class="fstat-no" title="function not covered" >&lt;T&gt;(t</span>arget: Observable&lt;T&gt;, prop: keyof Operators&lt;T&gt; | "subscribe" | "toPromise"): (Subscribe&lt;T&gt; | Promise&lt;T&gt; | InstanceType&lt;ProxyConstructor&gt;) =&gt; {
<span class="cstat-no" title="statement not covered" >        switch (prop) {</span>
            case "subscribe":
<span class="cstat-no" title="statement not covered" >                return <span class="fstat-no" title="function not covered" >(.</span>..args: Parameters&lt;typeof subscribe&gt;) =&gt; <span class="cstat-no" title="statement not covered" >subscribe&lt;T&gt;(...args)(target);</span></span>
            case "toPromise":
<span class="cstat-no" title="statement not covered" >                return <span class="fstat-no" title="function not covered" >() =</span>&gt; <span class="cstat-no" title="statement not covered" >toPromise&lt;T&gt;()(target);</span></span>
            default:
<span class="cstat-no" title="statement not covered" >                return (<span class="fstat-no" title="function not covered" >&lt;R&gt;(o</span>perator: (...args: any[]) =&gt; Operator&lt;T, R&gt;) =&gt; <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(.</span>..args: any[]) =&gt; <span class="cstat-no" title="statement not covered" >new Proxy(operator(...args)(target), rxProxy))</span></span>(operators[prop]);</span>
        }
    }
};
type Obs = {
    subscribe: typeof subscribe;
    toPromise: typeof toPromise;
};
type Op = {
    [key in keyof (typeof operators)]: (...args: Parameters&lt;((typeof operators))[key]&gt;) =&gt; Op;
} &amp; Obs;
type Rx = {
    [key in keyof typeof observables]: (...args: Parameters&lt;(typeof observables)[key]&gt;) =&gt; Op
};
export const rx: Rx = new Proxy(<span class="fstat-no" title="function not covered" >&lt;T&gt;(f</span>: Observable&lt;T&gt;) =&gt; (<span class="cstat-no" title="statement not covered" >inspect() ? new Node(f).pipe() : new Proxy(f, rxProxy))</span>, {
    get: <span class="fstat-no" title="function not covered" >(_</span>target: any, prop: keyof typeof observables) =&gt;
<span class="cstat-no" title="statement not covered" >        inspect()</span>
            ? <span class="fstat-no" title="function not covered" >(.</span>..arg: any[]) =&gt; <span class="cstat-no" title="statement not covered" >new Node(prop, arg).pipe()</span>
            : (<span class="fstat-no" title="function not covered" >&lt;T&gt;(o</span>bservable: (...args: any[]) =&gt; Observable&lt;T&gt;) =&gt; <span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >(.</span>..args: any[]) =&gt; <span class="cstat-no" title="statement not covered" >new Proxy(observable(...args), rxProxy))</span></span>(observables[prop]),
    // @ts-ignore
    set: <span class="fstat-no" title="function not covered" >(_</span>target, prop: string, value) =&gt; (<span class="cstat-no" title="statement not covered" >observables[prop] = value)</span>,
});</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank">istanbul</a>
                at Mon Sep 13 2021 08:41:07 GMT+0800 (中国标准时间)
            </div>
        </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    