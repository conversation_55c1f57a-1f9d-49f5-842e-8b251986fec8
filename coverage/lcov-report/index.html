
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">24.24% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>226/932</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.21% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>7/166</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">14.16% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>49/346</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">23.91% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>193/807</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="combination.ts"><a href="combination.ts.html">combination.ts</a></td>
	<td data-value="7.02" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 7%"></div><div class="cover-empty" style="width: 93%"></div></div>
	</td>
	<td data-value="7.02" class="pct low">7.02%</td>
	<td data-value="185" class="abs low">13/185</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="35" class="abs low">0/35</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="63" class="abs low">0/63</td>
	<td data-value="7.73" class="pct low">7.73%</td>
	<td data-value="168" class="abs low">13/168</td>
	</tr>

<tr>
	<td class="file low" data-value="common.ts"><a href="common.ts.html">common.ts</a></td>
	<td data-value="40.38" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 40%"></div><div class="cover-empty" style="width: 60%"></div></div>
	</td>
	<td data-value="40.38" class="pct low">40.38%</td>
	<td data-value="156" class="abs low">63/156</td>
	<td data-value="10" class="pct low">10%</td>
	<td data-value="30" class="abs low">3/30</td>
	<td data-value="39.68" class="pct low">39.68%</td>
	<td data-value="63" class="abs low">25/63</td>
	<td data-value="38.68" class="pct low">38.68%</td>
	<td data-value="137" class="abs low">53/137</td>
	</tr>

<tr>
	<td class="file low" data-value="filtering.ts"><a href="filtering.ts.html">filtering.ts</a></td>
	<td data-value="16.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="168" class="abs low">28/168</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="33" class="abs low">0/33</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="51" class="abs low">0/51</td>
	<td data-value="16.1" class="pct low">16.1%</td>
	<td data-value="149" class="abs low">24/149</td>
	</tr>

<tr>
	<td class="file high" data-value="index.ts"><a href="index.ts.html">index.ts</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="7" class="abs high">7/7</td>
	</tr>

<tr>
	<td class="file low" data-value="mathematical.ts"><a href="mathematical.ts.html">mathematical.ts</a></td>
	<td data-value="34.48" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 34%"></div><div class="cover-empty" style="width: 66%"></div></div>
	</td>
	<td data-value="34.48" class="pct low">34.48%</td>
	<td data-value="29" class="abs low">10/29</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="10" class="abs low">0/10</td>
	<td data-value="31.57" class="pct low">31.57%</td>
	<td data-value="19" class="abs low">6/19</td>
	</tr>

<tr>
	<td class="file low" data-value="producer.ts"><a href="producer.ts.html">producer.ts</a></td>
	<td data-value="28.66" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 28%"></div><div class="cover-empty" style="width: 72%"></div></div>
	</td>
	<td data-value="28.66" class="pct low">28.66%</td>
	<td data-value="150" class="abs low">43/150</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="27" class="abs low">3/27</td>
	<td data-value="13.88" class="pct low">13.88%</td>
	<td data-value="72" class="abs low">10/72</td>
	<td data-value="30.4" class="pct low">30.4%</td>
	<td data-value="125" class="abs low">38/125</td>
	</tr>

<tr>
	<td class="file low" data-value="transformation.ts"><a href="transformation.ts.html">transformation.ts</a></td>
	<td data-value="29.34" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 29%"></div><div class="cover-empty" style="width: 71%"></div></div>
	</td>
	<td data-value="29.34" class="pct low">29.34%</td>
	<td data-value="167" class="abs low">49/167</td>
	<td data-value="4.54" class="pct low">4.54%</td>
	<td data-value="22" class="abs low">1/22</td>
	<td data-value="19.67" class="pct low">19.67%</td>
	<td data-value="61" class="abs low">12/61</td>
	<td data-value="28.57" class="pct low">28.57%</td>
	<td data-value="154" class="abs low">44/154</td>
	</tr>

<tr>
	<td class="file low" data-value="utils.ts"><a href="utils.ts.html">utils.ts</a></td>
	<td data-value="18.57" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 18%"></div><div class="cover-empty" style="width: 82%"></div></div>
	</td>
	<td data-value="18.57" class="pct low">18.57%</td>
	<td data-value="70" class="abs low">13/70</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="15" class="abs low">0/15</td>
	<td data-value="7.69" class="pct low">7.69%</td>
	<td data-value="26" class="abs low">2/26</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="48" class="abs low">8/48</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-18T09:23:26.754Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    