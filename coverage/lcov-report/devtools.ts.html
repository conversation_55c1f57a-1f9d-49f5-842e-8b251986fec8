
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for devtools.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="index.html">All files</a> devtools.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.55% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>4/88</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/40</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/25</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">4.82% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>4/83</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a></td><td class="line-coverage quiet"><span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-yes">5x</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import { Events, ISink, Sink } from "./common";
import * as fastrx from './index';
let COUNT = 0;
type NodeSink = ISink&lt;unknown&gt; &amp; { streamId: number, nodeId: number; };
export class Node {
    source?: Node;
<span class="cstat-no" title="statement not covered" >    id: number = COUNT++;</span>
    end: boolean;
    subscribeSink?: Function;
<span class="fstat-no" title="function not covered" >    constructor(public <span class="cstat-no" title="statement not covered" >n</span>ame: string | { name: string; } = <span class="branch-0 cbranch-no" title="branch not covered" >'</span>',</span> public <span class="cstat-no" title="statement not covered" >arg: any[] = <span class="branch-0 cbranch-no" title="branch not covered" >[</span>])</span> {
<span class="cstat-no" title="statement not covered" >        switch (name) {</span>
            case 'subscribe':
            case 'toPromise':
            case 'toRef':
<span class="cstat-no" title="statement not covered" >                this.end = true;</span>
<span class="cstat-no" title="statement not covered" >                break;</span>
            default:
<span class="cstat-no" title="statement not covered" >                this.end = false;</span>
        }
<span class="cstat-no" title="statement not covered" >        Events.create(this);</span>
<span class="cstat-no" title="statement not covered" >        if (arg.length) {</span>
<span class="cstat-no" title="statement not covered" >            this.args = arg;</span>
        }
    }
<span class="fstat-no" title="function not covered" >    toString(</span>) {
<span class="cstat-no" title="statement not covered" >        return `${typeof this.name == 'string' ? this.name : this.name.name}(${this.arg</span>
            .map(<span class="fstat-no" title="function not covered" >(x</span>) =&gt;
<span class="cstat-no" title="statement not covered" >                typeof x == 'object' || typeof x == 'function'</span>
                    ? (typeof x.name == 'function' ? x.name.name : x.name) || '...'
                    : x
            )
            .join(',')})`;
    }
<span class="fstat-no" title="function not covered" >    get u</span>nProxy() {
<span class="cstat-no" title="statement not covered" >        return this;</span>
    }
    //是否属于子流
<span class="fstat-no" title="function not covered" >    checkSubNode(</span>x: Node | ((...args: any[]) =&gt; any)) {
<span class="cstat-no" title="statement not covered" >        if ("unProxy" in x) {</span>
            const isNode = <span class="cstat-no" title="statement not covered" >x.unProxy;</span>
<span class="cstat-no" title="statement not covered" >            Events.addSource(this, isNode),</span>
<span class="fstat-no" title="function not covered" >                (s</span>: NodeSink) =&gt; {
<span class="cstat-no" title="statement not covered" >                    s.nodeId = this.id;</span>
<span class="cstat-no" title="statement not covered" >                    s.streamId = 0;</span>
<span class="cstat-no" title="statement not covered" >                    isNode.subscribe(s);</span>
                };
        } else <span class="cstat-no" title="statement not covered" >if (typeof x == 'function') {</span>
<span class="cstat-no" title="statement not covered" ><span class="fstat-no" title="function not covered" >            (.</span>..arg: any[]): any =&gt; <span class="cstat-no" title="statement not covered" >this.checkSubNode(x(...arg));</span></span>
        }
<span class="cstat-no" title="statement not covered" >        return x;</span>
    }
    //过滤子事件流，放入sources数组中，就能显示
<span class="fstat-no" title="function not covered" >    set a</span>rgs(value: any[]) {
<span class="cstat-no" title="statement not covered" >        this.arg = value.map(<span class="fstat-no" title="function not covered" >(x</span>) =&gt; <span class="cstat-no" title="statement not covered" >this.checkSubNode(x))</span>;</span>
    }
    // 通过返回proxy产生链式调用
<span class="fstat-no" title="function not covered" >    pipe(</span>) {
<span class="cstat-no" title="statement not covered" >        if (this.end) {</span>
<span class="cstat-no" title="statement not covered" >            return this.subscribe();</span>
        }
        const target = <span class="cstat-no" title="statement not covered" >this;</span>
<span class="cstat-no" title="statement not covered" >        return new Proxy(<span class="fstat-no" title="function not covered" >(s</span>ink: NodeSink) =&gt; <span class="cstat-no" title="statement not covered" >this.subscribe(sink),</span> {</span>
<span class="fstat-no" title="function not covered" >            get(</span>_, prop: (keyof Node) | string) {
<span class="cstat-no" title="statement not covered" >                if (prop != 'subscribe' &amp;&amp; (prop in target))</span>
                    // @ts-ignore
<span class="cstat-no" title="statement not covered" >                    return target[prop];</span>
<span class="cstat-no" title="statement not covered" >                if (prop == 'subscribe' &amp;&amp; target.subscribeSink) <span class="cstat-no" title="statement not covered" >return target.subscribeSink;</span></span>
                const sink = <span class="cstat-no" title="statement not covered" >target.createSink(prop);</span>
<span class="cstat-no" title="statement not covered" >                return (target.subscribeSink = <span class="fstat-no" title="function not covered" >(.</span>..args: any[]) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    sink.args = args;</span>
<span class="cstat-no" title="statement not covered" >                    Events.update(sink);</span>
<span class="cstat-no" title="statement not covered" >                    return sink.pipe();</span>
                });
            },
        });
    }
<span class="fstat-no" title="function not covered" >    createSink(</span>name: string) {
        const sink = <span class="cstat-no" title="statement not covered" >new Node(name);</span>
<span class="cstat-no" title="statement not covered" >        sink.source = this;</span>
<span class="cstat-no" title="statement not covered" >        Events.pipe(sink);</span>
<span class="cstat-no" title="statement not covered" >        return sink;</span>
    }
<span class="fstat-no" title="function not covered" >    subscribe(</span>sink?: NodeSink): unknown {
        const { source, name, arg } = <span class="cstat-no" title="statement not covered" >this;</span>
        // @ts-ignore
        const realrx = <span class="cstat-no" title="statement not covered" >typeof name == 'string' ? fastrx[name](...arg) : name;</span>
        let f =
<span class="cstat-no" title="statement not covered" >            source &amp;&amp; !this.end</span>
                ? realrx(<span class="fstat-no" title="function not covered" >(s</span>: NodeSink) =&gt; {
<span class="cstat-no" title="statement not covered" >                    s.streamId = streamCount - 1;</span>
<span class="cstat-no" title="statement not covered" >                    s.nodeId = this.id;</span>
<span class="cstat-no" title="statement not covered" >                    source.subscribe(s);</span>
                })
                : realrx;
        let streamCount = <span class="cstat-no" title="statement not covered" >0;</span>
<span class="cstat-no" title="statement not covered" >        this.subscribe = <span class="fstat-no" title="function not covered" >function (s</span>ink?: NodeSink) {</span>
            const streamId = <span class="cstat-no" title="statement not covered" >streamCount++;</span>
<span class="cstat-no" title="statement not covered" >            if (sink) {</span>
                const newSink = <span class="cstat-no" title="statement not covered" >new Sink(sink);</span>
<span class="cstat-no" title="statement not covered" >                newSink.next = <span class="fstat-no" title="function not covered" >(d</span>ata) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    Events.next(this, streamId, data);</span>
<span class="cstat-no" title="statement not covered" >                    sink.next(data);</span>
                };
<span class="cstat-no" title="statement not covered" >                newSink.complete = <span class="fstat-no" title="function not covered" >() =</span>&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    Events.complete(this, streamId);</span>
<span class="cstat-no" title="statement not covered" >                    sink.complete();</span>
                };
<span class="cstat-no" title="statement not covered" >                newSink.error = <span class="fstat-no" title="function not covered" >(e</span>rr) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    Events.complete(this, streamId, err);</span>
<span class="cstat-no" title="statement not covered" >                    sink.error(err);</span>
                };
<span class="cstat-no" title="statement not covered" >                newSink.defer(<span class="fstat-no" title="function not covered" >() =</span>&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    Events.defer(this, streamId);</span>
                });
<span class="cstat-no" title="statement not covered" >                Events.subscribe(this, sink);</span>
<span class="cstat-no" title="statement not covered" >                f(newSink);</span>
<span class="cstat-no" title="statement not covered" >                return newSink;</span>
            }
<span class="cstat-no" title="statement not covered" >            return f(<span class="fstat-no" title="function not covered" >(s</span>: NodeSink) =&gt; {</span>
                const { next, complete, error } = <span class="cstat-no" title="statement not covered" >s;</span>
<span class="cstat-no" title="statement not covered" >                s.next = <span class="fstat-no" title="function not covered" >(d</span>ata: any) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    Events.next(this, streamId, data);</span>
<span class="cstat-no" title="statement not covered" >                    next(data);</span>
                };
<span class="cstat-no" title="statement not covered" >                s.error = <span class="fstat-no" title="function not covered" >(e</span>rr) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    Events.complete(this, streamId, err);</span>
<span class="cstat-no" title="statement not covered" >                    error(err);</span>
                };
<span class="cstat-no" title="statement not covered" >                s.complete = <span class="fstat-no" title="function not covered" >() =</span>&gt; {</span>
<span class="cstat-no" title="statement not covered" >                    Events.complete(this, streamId);</span>
<span class="cstat-no" title="statement not covered" >                    complete();</span>
                };
<span class="cstat-no" title="statement not covered" >                s.streamId = streamId;</span>
<span class="cstat-no" title="statement not covered" >                s.nodeId = this.id;</span>
<span class="cstat-no" title="statement not covered" >                Events.subscribe(this);</span>
<span class="cstat-no" title="statement not covered" >                source!.subscribe(s);</span>
            });
&nbsp;
        };
<span class="cstat-no" title="statement not covered" >        return this.subscribe(sink);</span>
    }
}
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank">istanbul</a>
                at Mon Sep 13 2021 08:41:07 GMT+0800 (中国标准时间)
            </div>
        </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    