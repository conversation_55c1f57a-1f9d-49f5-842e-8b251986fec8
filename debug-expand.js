// 简单的 expand 测试
const { pipe, of, expand, empty, subscribe } = require('./dist/cjs.js');

console.log('开始测试 expand...');

let resultCount = 0;
let completed = false;

pipe(
  of(1),
  expand(x => {
    console.log('expand project called with:', x);
    if (x < 3) {
      console.log('returning of(' + (x + 1) + ')');
      return of(x + 1);
    } else {
      console.log('returning empty()');
      return empty();
    }
  }),
  subscribe(
    (d) => {
      console.log('expand result:', d, ', count:', ++resultCount);
    },
    (err) => {
      console.log('expand error:', err);
    },
    () => {
      console.log('expand completed, total results:', resultCount);
      completed = true;
    }
  )
);

// 检查是否在合理时间内完成
setTimeout(() => {
  if (!completed) {
    console.log('测试超时，结果数量:', resultCount);
    process.exit(1);
  } else {
    console.log('测试成功完成');
    process.exit(0);
  }
}, 1000);
