// 测试 expand 操作符
import { of, expand, take } from './es/index.js';

// 测试用例 1: 基本的 expand 功能
console.log('测试用例 1: 基本的 expand 功能');
of(1)
  .expand(x => x < 5 ? of(x + 1) : of())
  .take(5)
  .subscribe(console.log);

console.log('\n测试用例 2: 递归展开数组');
of([1, 2])
  .expand(arr => arr.length < 4 ? of([...arr, arr.length + 1]) : of())
  .take(3)
  .subscribe(console.log);

console.log('\n测试用例 3: 空的展开');
of(10)
  .expand(x => of()) // 立即返回空 Observable
  .subscribe(
    value => console.log('值:', value),
    error => console.log('错误:', error),
    () => console.log('完成')
  );
