{"name": "rx-show", "version": "0.0.0", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build", "serve": "vite preview"}, "dependencies": {"@vitejs/plugin-vue-jsx": "^1.1.2", "ant-design-vue": "^2.0.1", "core-js": "^3.6.5", "less": "2.7.0", "less-loader": "5.0.0", "vue": "^3.0.7"}, "devDependencies": {"@vitejs/plugin-vue": "^1.1.5", "@vue/babel-plugin-jsx": "^1.0.4", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.0.7", "vite": "^2.0.5"}}